<?php
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Simple test page to verify API parameter handling
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

$page_title = "API Parameter Test";
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <h2>API Parameter Handling Test</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>GET Request Test (URL Parameters)</h5>
                </div>
                <div class="card-body">
                    <button id="test-get" class="btn btn-primary">Test GET Request</button>
                    <div id="get-result" class="mt-3"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>POST Request Test (Body Parameters)</h5>
                </div>
                <div class="card-body">
                    <button id="test-post" class="btn btn-success">Test POST Request</button>
                    <div id="post-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('test-get').addEventListener('click', async function() {
    const resultDiv = document.getElementById('get-result');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Testing...';
    
    try {
        const response = await fetch('api.php?entity=rooms&action=list');
        const data = await response.json();
        
        resultDiv.innerHTML = `
            <div class="alert alert-${data.success ? 'success' : 'danger'}">
                <strong>Result:</strong> ${data.success ? 'Success' : 'Failed'}<br>
                <strong>Data:</strong> ${JSON.stringify(data, null, 2)}
            </div>
        `;
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    }
});

document.getElementById('test-post').addEventListener('click', async function() {
    const resultDiv = document.getElementById('post-result');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm"></div> Testing...';
    
    try {
        const formData = new FormData();
        formData.append('entity', 'patients');
        formData.append('action', 'add');
        formData.append('name', 'Test Patient ' + Date.now());
        formData.append('dob', '1990-01-01');
        
        const response = await fetch('api.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();
        
        resultDiv.innerHTML = `
            <div class="alert alert-${data.success ? 'success' : 'danger'}">
                <strong>Result:</strong> ${data.success ? 'Success' : 'Failed'}<br>
                <strong>Data:</strong> ${JSON.stringify(data, null, 2)}
            </div>
        `;
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
