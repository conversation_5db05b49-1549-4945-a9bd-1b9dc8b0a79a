<?php
function handle_calendar_details($action, $method, $db, $input = [])
{
    if ($method === 'GET') {
        $room_id = $_GET['room_id'] ?? null;
        $date = $_GET['date'] ?? null;
        
        if (!$room_id || !$date) {
            return ['success' => false, 'error' => 'Room ID and date are required'];
        }
        
        try {
            // Get consultations with agency filtering
            $consultSql = "
                SELECT a.id, p.name as name, a.subtype, a.start_time, a.end_time
                FROM appointments a
                JOIN patients p ON a.patient_id = p.id
                WHERE a.room_id = ? AND a.appointment_date = ? AND a.type = 'consult'
            ";
            $consultParams = [$room_id, $date];

            // Add agency filtering for non-admin users
            if (isset($_SESSION['role']) && $_SESSION['role'] !== 'admin' && isset($_SESSION['agency_id'])) {
                $consultSql .= " AND p.agency_id = ?";
                $consultParams[] = $_SESSION['agency_id'];
            }

            $consultSql .= " ORDER BY a.start_time";

            $stmt = $db->prepare($consultSql);
            $stmt->execute($consultParams);
            $consults = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get cosmetic procedures with agency filtering
            $cosmeticSql = "
                SELECT a.id, p.name as name, a.subtype, a.start_time, a.end_time
                FROM appointments a
                JOIN patients p ON a.patient_id = p.id
                WHERE a.room_id = ? AND a.appointment_date = ? AND a.type = 'cosmetic'
            ";
            $cosmeticParams = [$room_id, $date];

            // Add agency filtering for non-admin users
            if (isset($_SESSION['role']) && $_SESSION['role'] !== 'admin' && isset($_SESSION['agency_id'])) {
                $cosmeticSql .= " AND p.agency_id = ?";
                $cosmeticParams[] = $_SESSION['agency_id'];
            }

            $cosmeticSql .= " ORDER BY a.start_time";

            $stmt = $db->prepare($cosmeticSql);
            $stmt->execute($cosmeticParams);
            $cosmetics = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get surgery details with agency filtering
            $surgerySql = "
                SELECT s.id, p.name as patient_name, s.graft_count, s.status, p.agency_id
                FROM room_reservations rr
                JOIN surgeries s ON rr.surgery_id = s.id
                JOIN patients p ON s.patient_id = p.id
                WHERE rr.room_id = ? AND rr.reserved_date = ?
            ";
            $surgeryParams = [$room_id, $date];

            // Add agency filtering for non-admin users
            if (isset($_SESSION['role']) && $_SESSION['role'] !== 'admin' && isset($_SESSION['agency_id'])) {
                $surgerySql .= " AND p.agency_id = ?";
                $surgeryParams[] = $_SESSION['agency_id'];
            }

            $stmt = $db->prepare($surgerySql);
            $stmt->execute($surgeryParams);
            $surgery = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $surgery_details = null;
            if ($surgery) {
                $surgery_details = [
                    'patient_name' => $surgery['patient_name'],
                    'procedure' => 'Hair Transplant',
                    'graft_count' => $surgery['graft_count'],
                    'status' => $surgery['status'],
                    'time' => '08:00-17:00' // Default time for surgeries
                ];
            }
            
            return [
                'success' => true,
                'consult' => $consults,
                'cosmetic' => $cosmetics,
                'surgery' => $surgery_details
            ];
        } catch (PDOException $e) {
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    return ['success' => false, 'error' => 'Invalid method for calendar_details entity'];
}